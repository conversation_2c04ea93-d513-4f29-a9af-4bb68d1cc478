:root {
  --background: oklch(0.9900 0.0020 106.0000);
  --foreground: oklch(0.2000 0.0050 264.0000);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.2000 0.0050 264.0000);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.2000 0.0050 264.0000);
  --primary: oklch(0.5500 0.1800 230.0000);
  --primary-foreground: oklch(0.9900 0.0020 106.0000);
  --secondary: oklch(0.9600 0.0100 220.0000);
  --secondary-foreground: oklch(0.3000 0.0080 264.0000);
  --muted: oklch(0.9700 0.0050 220.0000);
  --muted-foreground: oklch(0.5000 0.0100 264.0000);
  --accent: oklch(0.9400 0.0200 210.0000);
  --accent-foreground: oklch(0.2500 0.0080 264.0000);
  --destructive: oklch(0.6200 0.2000 25.0000);
  --destructive-foreground: oklch(0.9900 0.0020 106.0000);
  --border: oklch(0.9200 0.0100 220.0000);
  --input: oklch(0.9800 0.0050 220.0000);
  --ring: oklch(0.5500 0.1800 230.0000);
  --chart-1: oklch(0.5500 0.1800 230.0000);
  --chart-2: oklch(0.6000 0.1600 180.0000);
  --chart-3: oklch(0.5800 0.1700 280.0000);
  --chart-4: oklch(0.6200 0.1500 320.0000);
  --chart-5: oklch(0.5600 0.1900 60.0000);
  --sidebar: oklch(0.9800 0.0050 220.0000);
  --sidebar-foreground: oklch(0.2000 0.0050 264.0000);
  --sidebar-primary: oklch(0.5500 0.1800 230.0000);
  --sidebar-primary-foreground: oklch(0.9900 0.0020 106.0000);
  --sidebar-accent: oklch(0.9400 0.0200 210.0000);
  --sidebar-accent-foreground: oklch(0.2500 0.0080 264.0000);
  --sidebar-border: oklch(0.9200 0.0100 220.0000);
  --sidebar-ring: oklch(0.5500 0.1800 230.0000);
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 2px 0 hsl(220 13% 91% / 0.3);
  --shadow-xs: 0 1px 3px 0 hsl(220 13% 91% / 0.4);
  --shadow-sm: 0 1px 3px 0 hsl(220 13% 91% / 0.5), 0 1px 2px -1px hsl(220 13% 91% / 0.4);
  --shadow: 0 2px 4px 0 hsl(220 13% 91% / 0.6), 0 1px 2px -1px hsl(220 13% 91% / 0.5);
  --shadow-md: 0 4px 6px -1px hsl(220 13% 91% / 0.7), 0 2px 4px -2px hsl(220 13% 91% / 0.6);
  --shadow-lg: 0 10px 15px -3px hsl(220 13% 91% / 0.8), 0 4px 6px -4px hsl(220 13% 91% / 0.7);
  --shadow-xl: 0 20px 25px -5px hsl(220 13% 91% / 0.9), 0 8px 10px -6px hsl(220 13% 91% / 0.8);
  --shadow-2xl: 0 25px 50px -12px hsl(220 13% 91% / 1.0);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}