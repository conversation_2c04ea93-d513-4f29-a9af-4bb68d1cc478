# 知识图谱性能优化使用指南

## 🚀 快速开始

### 问题解决
如果你遇到了画布拖动卡顿的问题，这个优化方案已经自动启用。无需额外配置即可享受流畅的拖动体验。

### 验证优化效果
1. 打开浏览器开发者工具控制台
2. 查看是否有以下日志：
   ```
   🛡️ 动态性能安全区域已启用!
   🎯 PixiJS 自动裁剪优化已启用
   ```

## 🎛️ 实时调试

### 基本调试命令
在浏览器控制台中输入以下命令：

```javascript
// 查看当前安全区域状态
getSafeAreaInfo()

// 调整安全区域参数（推荐设置）
adjustSafeArea(8000, 8000, 100, 2.5, 2.0, 25000, 25000)

// 查看画布详细信息
debugCanvasInfo()
```

### 参数说明
- **baseWidth/baseHeight**: 基础安全区域大小（推荐 6000-10000）
- **margin**: 安全边距（推荐 50-200）
- **contentFactor**: 内容扩展系数（推荐 2.0-3.0）
- **scaleFactor**: 缩放扩展系数（推荐 1.5-2.5）
- **maxWidth/maxHeight**: 最大限制（推荐 20000-30000）

## 🔧 常见问题解决

### 1. 拖动范围太小
```javascript
// 增加基础区域大小
adjustSafeArea(10000, 10000)
```

### 2. 高缩放时移动受限
```javascript
// 增加缩放扩展系数
adjustSafeArea(null, null, null, null, 2.5)
```

### 3. 大图谱边界不够
```javascript
// 增加内容扩展系数
adjustSafeArea(null, null, null, 3.0)
```

### 4. 性能仍有问题
```javascript
// 减少最大限制
adjustSafeArea(null, null, null, null, null, 15000, 15000)
```

## 📊 性能监控

### 查看性能指标
控制台会显示详细的性能信息：
- 当前缩放级别
- 屏幕尺寸
- 内容大小
- 动态安全区域大小
- 实际边界范围

### 性能优化提示
- 绿色日志：正常运行
- 黄色日志：性能警告
- 红色日志：需要注意的问题

## 🎯 最佳实践

### 1. 根据数据量调整
- **小图谱** (< 100 节点): 使用默认设置
- **中图谱** (100-1000 节点): 适当增加基础区域
- **大图谱** (> 1000 节点): 增加内容扩展系数

### 2. 根据设备性能调整
- **高性能设备**: 可以增加最大限制
- **低性能设备**: 减少最大限制，优先保证流畅度

### 3. 根据使用场景调整
- **展示模式**: 较小的安全区域即可
- **编辑模式**: 需要更大的移动范围
- **演示模式**: 平衡性能和视觉效果

## 🔄 动态调整示例

### 场景1：数据加载后优化
```javascript
// 数据加载完成后，根据实际内容调整
setTimeout(() => {
  const info = getSafeAreaInfo()
  console.log('当前内容大小:', info.bounds)
  
  // 如果内容很大，增加扩展系数
  if (info.bounds.maxX - info.bounds.minX > 5000) {
    adjustSafeArea(null, null, null, 3.0)
  }
}, 1000)
```

### 场景2：性能自适应
```javascript
// 监控性能，自动调整
setInterval(() => {
  const fps = window.currentFPS || 60
  if (fps < 30) {
    // 性能不足，减少安全区域
    adjustSafeArea(null, null, null, null, null, 15000, 15000)
    console.log('🔧 自动降低安全区域以提升性能')
  }
}, 5000)
```

## 📱 移动设备优化

### 触摸设备特殊设置
```javascript
// 检测移动设备
if (/Mobi|Android/i.test(navigator.userAgent)) {
  // 移动设备使用更保守的设置
  adjustSafeArea(6000, 6000, 50, 1.8, 1.3, 15000, 15000)
  console.log('📱 已应用移动设备优化设置')
}
```

## 🎨 视觉调试

### 启用边界可视化（开发模式）
```javascript
// 在开发环境中可以启用边界可视化
if (process.env.NODE_ENV === 'development') {
  window.showSafeBounds = true
  console.log('🎨 安全边界可视化已启用')
}
```

## 📈 性能提升预期

使用此优化方案后，你应该能看到：

- ✅ **拖动流畅度**: 60 FPS 稳定拖动
- ✅ **缩放响应**: 无延迟的缩放操作
- ✅ **内存使用**: 优化的内存占用
- ✅ **CPU 使用**: 降低的 CPU 负载
- ✅ **用户体验**: 流畅的交互体验

## 🆘 故障排除

### 如果优化没有生效
1. 检查控制台是否有错误信息
2. 确认 PixiJS 和 D3.js 版本正确
3. 尝试刷新页面重新加载
4. 检查浏览器是否支持 WebGL

### 联系支持
如果问题仍然存在，请提供：
- 浏览器版本和设备信息
- 控制台错误信息
- 图谱数据规模
- 具体的性能问题描述

---

💡 **提示**: 这个优化方案会根据你的具体使用情况自动调整，大多数情况下无需手动配置即可获得最佳性能。
