<template>
  <div class="performance-settings">
    <h3 class="section-title">🎮 性能设置</h3>

    <div class="settings-list">
      <!-- GPU 加速开关 -->
      <div class="setting-item">
        <label class="setting-label">
          <input
            type="checkbox"
            class="checkbox"
            v-model="uiState.gpuAcceleration"
            @change="handleGPUToggle"
          />
          <span class="setting-text">GPU 加速</span>
        </label>
        <div class="setting-description">
          使用 WebGL 渲染提升性能
        </div>
      </div>

      <!-- FPS 显示开关 -->
      <div class="setting-item">
        <label class="setting-label">
          <input
            type="checkbox"
            class="checkbox"
            v-model="uiState.showFPS"
            @change="handleFPSToggle"
          />
          <span class="setting-text">FPS 显示</span>
        </label>
        <div class="setting-description">
          显示实时帧率监控
        </div>
      </div>

      <!-- 最大节点数滑块 -->
      <div class="setting-item">
        <div class="setting-label">
          <span class="setting-text">最大节点数</span>
          <span class="setting-value">{{ graphData.maxNodes }}</span>
        </div>
        <input
          type="range"
          class="slider"
          min="100"
          max="5000"
          step="100"
          v-model.number="graphData.maxNodes"
          @input="handleMaxNodesChange"
        />
        <div class="slider-labels">
          <span>100</span>
          <span>5000</span>
        </div>
        <div class="setting-description">
          限制同时显示的节点数量
        </div>
      </div>

      <!-- 目标 FPS 设置 -->
      <div class="setting-item">
        <div class="setting-label">
          <span class="setting-text">目标 FPS</span>
          <span class="setting-value">{{ uiState.targetFPS }}</span>
        </div>
        <input
          type="range"
          class="slider"
          min="30"
          max="120"
          step="10"
          v-model.number="uiState.targetFPS"
          @input="handleTargetFPSChange"
        />
        <div class="slider-labels">
          <span>30</span>
          <span>120</span>
        </div>
        <div class="setting-description">
          设置渲染目标帧率
        </div>
      </div>
    </div>

    <!-- 性能建议 */
    <div class="performance-tips">
      <div class="tips-title">💡 性能建议</div>
      <div class="tip-item" v-for="tip in performanceTips" :key="tip.id">
        <span class="tip-icon">{{ tip.icon }}</span>
        <span class="tip-text">{{ tip.text }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useUIStateStore } from '../../stores/knowledge-graph/uiState.js'
import { useGraphDataStore } from '../../stores/knowledge-graph/graphData.js'

const uiState = useUIStateStore()
const graphData = useGraphDataStore()

/**
 * 处理 GPU 加速切换
 */
function handleGPUToggle() {
  // 这里可以添加 GPU 加速切换的逻辑
  console.log('GPU 加速:', uiState.gpuAcceleration ? '开启' : '关闭')
}

/**
 * 处理 FPS 显示切换
 */
function handleFPSToggle() {
  console.log('FPS 显示:', uiState.showFPS ? '开启' : '关闭')
}

/**
 * 处理最大节点数变化
 */
function handleMaxNodesChange() {
  console.log('最大节点数设置为:', graphData.maxNodes)
}

/**
 * 处理目标 FPS 变化
 */
function handleTargetFPSChange() {
  console.log('目标 FPS 设置为:', uiState.targetFPS)
}

/**
 * 性能建议
 */
const performanceTips = computed(() => {
  const tips = []

  if (graphData.stats.visibleNodes > 2000) {
    tips.push({
      id: 'nodes',
      icon: '⚠️',
      text: '节点数量较多，建议启用筛选'
    })
  }

  if (!uiState.gpuAcceleration) {
    tips.push({
      id: 'gpu',
      icon: '🚀',
      text: '启用 GPU 加速可提升渲染性能'
    })
  }

  if (uiState.targetFPS > 60 && graphData.stats.visibleNodes > 1000) {
    tips.push({
      id: 'fps',
      icon: '🎯',
      text: '高 FPS + 大量节点可能影响性能'
    })
  }

  if (tips.length === 0) {
    tips.push({
      id: 'good',
      icon: '✅',
      text: '当前设置已优化'
    })
  }

  return tips
})
</script>

<style scoped>
.performance-settings {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(78, 205, 196, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #4ecdc4;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.setting-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  cursor: pointer;
}

.setting-label input[type="checkbox"] {
  margin-right: 8px;
}

.setting-text {
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
}

.setting-value {
  font-size: 14px;
  color: #4ecdc4;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.setting-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

.slider {
  width: 100%;
  margin: 8px 0;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 4px;
}

.performance-tips {
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tips-title {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-weight: 600;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 12px;
}

.tip-text {
  flex: 1;
  line-height: 1.4;
}

/* 复选框样式覆盖 */
.setting-label .checkbox {
  margin-right: 8px;
}

/* 滑块悬停效果 */
.slider:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 设置项悬停效果 */
.setting-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(78, 205, 196, 0.3);
}

/* 响应式调整 */
@media (max-width: 480px) {
  .setting-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .setting-value {
    align-self: flex-end;
  }
}
</style>
